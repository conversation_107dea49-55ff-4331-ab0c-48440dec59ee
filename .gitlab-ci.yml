image:
  name: gitlab.elgentos.nl:4567/containers/deployment-runner:8.3
  entrypoint: [""]

variables:
  AUGMENT_SESSION_AUTH: "$AUGMENT_SESSION_AUTH"
  GITLAB_PRIVATE_TOKEN: "$GITLAB_PRIVATE_TOKEN" # PAT or Project Access Token with api scope

stages:
  - auggie

auggie:
  stage: auggie
  before_script:
    - |
      # Node 22 + Auggie (install nvm if missing)
      source ~/.nvm/nvm.sh
      nvm install 22
      nvm use 22
      npm install -g @augmentcode/auggie

    # Auggie permissions (copied from repo root)
    - mkdir -p /tmp/.augment
    - cp settings.json /tmp/.augment/settings.json

    # Instruction file (compose static header from repo + dynamic payload)
    - |
      ISSUE_TITLE="$(echo "$PAYLOAD" | base64 -d | jq -r '.object_attributes.title')"
      ISSUE_DESC="$(echo "$PAYLOAD" | base64 -d | jq -r '.object_attributes.description')"
      PROJECT_PATH="$(echo "$PAYLOAD" | base64 -d | jq -r '.project.path_with_namespace')"
      cat instructions_header.md > /tmp/auggie_instructions.md
      {
        echo
        echo "ISSUE TITLE:"
        echo "$ISSUE_TITLE"
        echo
        echo "ISSUE DESCRIPTION:"
        echo "$ISSUE_DESC"
      } >> /tmp/auggie_instructions.md

    - git clone --depth 1 "https://oauth2:$GITLAB_PRIVATE_TOKEN@$CI_SERVER_HOST/$PROJECT_PATH.git" magento2
    - cp .augmentignore magento2/
    - cd magento2

  script:
    - git config user.name "Señor Code"
    - git config user.email "<EMAIL>"
    - git fetch && git checkout -b "$BRANCHNAME"

    # Ensure Node is in this shell too
    - |
      export NVM_DIR="${NVM_DIR:-$HOME/.nvm}"
      . "$NVM_DIR/nvm.sh"
      nvm use 22

    # Run Auggie and capture output
    - |
      set -e
      export AUGMENT_SESSION_AUTH="${AUGMENT_SESSION_AUTH}"
      auggie --compact \
        --instruction-file /tmp/auggie_instructions.md \
        --workspace-root "$(pwd)" \
        --augment-cache-dir /tmp/.augment \
        --print | tee /tmp/auggie.out

      # Stage everything first (so new files are detected), but keep vendor/var out just in case
      git add -A -- . ':(exclude)vendor/' ':(exclude)var/'

      # Commit only if the index differs from HEAD
      if ! git diff --cached --quiet; then
        CC_LINE="$(grep -E '^(feat|fix|chore|refactor|docs|perf|test)(\(|:)' /tmp/auggie.out | tail -n1 || true)"
        [ -z "$CC_LINE" ] && CC_LINE="chore: apply Auggie changes for ${CI_COMMIT_SHORT_SHA:-pipeline}"
        git commit -m "$CC_LINE"
      else
        echo "No changes made by Auggie."
        echo "Summary:"; git status --porcelain=v1
      fi

      # Push (force) and CAPTURE the server output
      TARGET_BRANCH="${TARGET_BRANCH:-${CI_DEFAULT_BRANCH:-master}}"
      MR_TITLE="${ISSUE_TITLE:-$CC_LINE}"

      git push origin --force \
        HEAD:refs/heads/"$BRANCHNAME" \
        -o merge_request.create \
        -o merge_request.title="$MR_TITLE" \
        -o merge_request.target="$TARGET_BRANCH" \
        -o merge_request.remove_source_branch \
        $( [ -n "$AUTODEPLOY" ] && echo ' -o ci.variable="AUTODEPLOY=1"' ) 2>&1 | tee /tmp/gitpush.out

      # Parse MR URL/IID from push output (most reliable)
      MR_URL="$(grep -Eo 'https?://[^ ]+/-/merge_requests/[0-9]+' /tmp/gitpush.out | tail -n1 || true)"
      MR_IID="$(echo "$MR_URL" | sed -E 's#.*/([0-9]+)$#\1#' || true)"
      # Extract project path from the MR URL if present (handles subgroups)
      MR_PROJECT_PATH="$(echo "$MR_URL" | sed -E 's#https?://[^/]+/(.+)/-/merge_requests/[0-9]+#\1#' || true)"

      echo "DEBUG: BRANCHNAME=$BRANCHNAME TARGET_BRANCH=$TARGET_BRANCH"
      echo "DEBUG: MR_URL=${MR_URL:-<none>} MR_IID=${MR_IID:-<none>} MR_PROJECT_PATH=${MR_PROJECT_PATH:-<none>}"

      # Determine the TARGET project for API calls:
      if [ -n "$MR_PROJECT_PATH" ]; then
        TARGET_PROJECT_PATH="$MR_PROJECT_PATH"
      else
        TARGET_PROJECT_PATH="${PROJECT_PATH}"
      fi

      # URL-encode the path with slashes as %2F (GitLab requirement)
      ENC_PATH="$(jq -rn --arg p "$TARGET_PROJECT_PATH" '$p|@uri')"

      # Resolve the numeric project ID of the *magento2* repo
      HDR="PRIVATE-TOKEN: ${GITLAB_PRIVATE_TOKEN}"
      TARGET_PROJECT_ID="$(
        curl -sS --header "$HDR" "$CI_API_V4_URL/projects/$ENC_PATH" | jq -r '.id // empty'
      )"

      echo "DEBUG: TARGET_PROJECT_PATH=$TARGET_PROJECT_PATH TARGET_PROJECT_ID=${TARGET_PROJECT_ID:-<empty>}"

      # If MR_IID wasn't in push output, try to resolve it via API
      if [ -z "$MR_IID" ] && [ -n "$TARGET_PROJECT_ID" ]; then
        BR_ENC="$(jq -rn --arg s "$BRANCHNAME" '$s|@uri')"
        TB_ENC="$(jq -rn --arg s "$TARGET_BRANCH" '$s|@uri')"
        for i in 1 2 3 4 5; do
          MR_JSON="$(
            curl -sS --header "$HDR" \
              "$CI_API_V4_URL/projects/$TARGET_PROJECT_ID/merge_requests?state=opened&source_branch=$BR_ENC&target_branch=$TB_ENC&per_page=1&order_by=updated_at&sort=desc"
          )"
          echo "DEBUG: MR lookup attempt $i response: $MR_JSON" | sed 's/PRIVATE-TOKEN:[^ ]\+/PRIVATE-TOKEN:***HIDDEN***/g'
          MR_IID="$(printf '%s' "$MR_JSON" | jq -r 'if type=="array" and length>0 then .[0].iid else empty end' || true)"
          [ -n "$MR_IID" ] && break
          sleep 1
        done
      fi
      # Build JSON for description with full Auggie output
      jq -n \
        --arg cc "$CC_LINE" \
        --arg original_project_id "$ORIGINAL_PROJECT_ID" \
        --arg original_issue_iid "$ORIGINAL_ISSUE_IID" \
        --rawfile body /tmp/auggie.out \
        '{
          description:
            ("**Conventional Commit**: `\($cc)`\n\n" +
             if ($original_project_id != "" and $original_issue_iid != "") then
               "Belongs to " + $original_project_id + "#" + $original_issue_iid + "\n\n"
             else "" end +
             "**Auggie output**:\n\n```\n\($body)\n```")
        }' > /tmp/mr_payload.json

      # Update MR description or fall back to a note
      if [ -n "$MR_IID" ] && [ -n "$TARGET_PROJECT_ID" ]; then
        UPDATE_URL="$CI_API_V4_URL/projects/$TARGET_PROJECT_ID/merge_requests/$MR_IID"
        HTTP_CODE="$(
          curl -sS -o /tmp/mr_update.out -w '%{http_code}' -X PUT \
            --header "$HDR" \
            --header "Content-Type: application/json" \
            --data-binary @/tmp/mr_payload.json \
            "$UPDATE_URL"
        )"
        echo "DEBUG: PUT $UPDATE_URL -> HTTP $HTTP_CODE"
        if [ "$HTTP_CODE" != "200" ]; then
          echo "WARN: MR description update returned $HTTP_CODE; falling back to MR note."
          echo "DEBUG: Update response body:"; head -c 1000 /tmp/mr_update.out || true

          jq -n \
            --rawfile body /tmp/auggie.out \
            '{
              body: "**Auggie output (full)**:\n\n```\n\($body)\n```"
            }' > /tmp/mr_note.json

          NOTE_URL="$CI_API_V4_URL/projects/$TARGET_PROJECT_ID/merge_requests/$MR_IID/notes"
          NOTE_CODE="$(
            curl -sS -o /tmp/mr_note.out -w '%{http_code}' -X POST \
              --header "$HDR" \
              --header "Content-Type: application/json" \
              --data-binary @/tmp/mr_note.json \
              "$NOTE_URL"
          )"
          echo "DEBUG: POST $NOTE_URL -> HTTP $NOTE_CODE"
          if [ "$NOTE_CODE" != "201" ]; then
            echo "WARN: Failed to post MR note; response body (truncated):"
            head -c 1000 /tmp/mr_note.out || true
          fi
        fi
      else
        echo "WARN: Missing MR_IID or TARGET_PROJECT_ID; cannot update MR."
        echo "DEBUG: git push output (tail):"; tail -n 30 /tmp/gitpush.out || true
      fi

  only:
    - triggers
