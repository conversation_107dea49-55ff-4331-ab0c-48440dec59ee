{"object_kind": "issue", "event_type": "issue", "user": {"id": 2, "name": "<PERSON>", "username": "peter<PERSON><PERSON>", "avatar_url": "https://gitlab.elgentos.nl/uploads/-/system/user/avatar/2/avatar.png", "email": "[REDACTED]"}, "project": {"id": 820, "name": "Moma Beauty - Magento 2", "description": "", "web_url": "https://gitlab.elgentos.nl/momabeauty/magento2", "avatar_url": "https://gitlab.elgentos.nl/uploads/-/system/project/avatar/820/momabeauty.png", "git_ssh_url": "**********************:momabeauty/magento2.git", "git_http_url": "https://gitlab.elgentos.nl/momabeauty/magento2.git", "namespace": "<PERSON><PERSON>", "visibility_level": 10, "path_with_namespace": "momabeauty/magento2", "default_branch": "master", "ci_config_path": null, "homepage": "https://gitlab.elgentos.nl/momabeauty/magento2", "url": "**********************:momabeauty/magento2.git", "ssh_url": "**********************:momabeauty/magento2.git", "http_url": "https://gitlab.elgentos.nl/momabeauty/magento2.git"}, "object_attributes": {"author_id": 2, "closed_at": null, "confidential": false, "created_at": "2025-02-10 15:20:49 +0100", "description": "**Vertalingen in de checkout corrigeren**  \n\nAls gebruiker wil ik dat alle teksten in de checkout in het Nederlands worden weergegeven, zodat de gebruikerservaring consistent en begrijpelijk blijft.  \n\n**Acceptatiecriteria**  \n* De teksten \"Business\" en \"Private\" worden correct vertaald naar \"Zakelijk\" en \"Privé\".  \n* Alle andere teksten in de checkout worden gecontroleerd op taalconsistentie.  \n* De wijzigingen zijn zichtbaar op zowel desktop als mobiele apparaten.  \n\n**Tech notes**  \n* Controleer of de vertalingen worden geladen vanuit een vertaalbestand of hardcoded zijn.  \n* Pas de vertalingen aan in de juiste taalbestanden of configuratie.  \n* Test of de wijzigingen correct worden toegepast in verschillende checkout-scenario's.", "discussion_locked": null, "due_date": null, "id": 40130, "iid": 1341, "last_edited_at": null, "last_edited_by_id": null, "milestone_id": null, "moved_to_id": null, "duplicated_to_id": null, "project_id": 820, "relative_position": 410339, "state_id": 1, "time_estimate": 0, "title": "Vertalingen in de checkout corrigeren", "updated_at": "2025-02-10 15:24:25 +0100", "updated_by_id": 2, "weight": null, "health_status": null, "type": "Issue", "url": "https://gitlab.elgentos.nl/momabeauty/magento2/-/issues/1341", "total_time_spent": 0, "time_change": 0, "human_total_time_spent": null, "human_time_change": null, "human_time_estimate": null, "assignee_ids": [2], "assignee_id": 2, "labels": [{"id": 6656, "title": "AI Agent", "color": "#6699cc", "project_id": 820, "created_at": "2025-02-10 15:18:21 +0100", "updated_at": "2025-02-10 15:18:21 +0100", "template": false, "description": "", "type": "ProjectLabel", "group_id": null}], "state": "opened", "severity": "unknown", "customer_relations_contacts": [], "action": "update"}, "labels": [{"id": 6656, "title": "AI Agent", "color": "#6699cc", "project_id": 820, "created_at": "2025-02-10 15:18:21 +0100", "updated_at": "2025-02-10 15:18:21 +0100", "template": false, "description": "", "type": "ProjectLabel", "group_id": null}], "changes": {"updated_at": {"previous": "2025-02-10 15:20:49 +0100", "current": "2025-02-10 15:24:25 +0100"}, "updated_by_id": {"previous": null, "current": 2}}, "repository": {"name": "Moma Beauty - Magento 2", "url": "**********************:momabeauty/magento2.git", "description": "", "homepage": "https://gitlab.elgentos.nl/momabeauty/magento2"}, "assignees": [{"id": 2, "name": "<PERSON>", "username": "peter<PERSON><PERSON>", "avatar_url": "https://gitlab.elgentos.nl/uploads/-/system/user/avatar/2/avatar.png", "email": "[REDACTED]"}]}